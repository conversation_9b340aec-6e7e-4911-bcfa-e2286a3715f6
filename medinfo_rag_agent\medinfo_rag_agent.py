import os
from datetime import datetime
from typing import List, Dict, Optional
import streamlit as st
from groq import Groq
from dotenv import load_dotenv
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_pinecone import PineconeVectorStore
from pinecone import Pinecone, ServerlessSpec
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

# Configuração do modelo HuggingFaceEmbeddings.
embedder = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")

# Nome do índice no Pinecone.
INDEX_NAME = "medinfo"

# Classe de conexão API Groq.
class GroqClient:
    def __init__(self, api_key: str, model: str = "llama-3.1-8b-instant"):
        self.api_key = api_key
        self.model = model
        self.client = Groq(api_key=api_key)
        
    def chat(self, messages: List[Dict[str, str]]) -> str:
        try:
            completion = self.client.chat.completions.create(
                messages=messages,
                model=self.model
            )
            return completion.choices[0].message.content
        except Exception as e:
            return f"Error generating response: {str(e)}"

# Classe de agente personalizado.
class CustomAgent:
    def __init__(
        self, 
        name: str, 
        model: GroqClient, 
        instructions: str = "", 
        markdown: bool = True,
        show_tool_calls: bool = False
    ):
        self.name = name
        self.model = model
        self.instructions = instructions
        self.markdown = markdown
        self.show_tool_calls = show_tool_calls
        
    def run(self, query: str) -> Dict:
        """Executa o agente com uma consulta"""
        messages = []
        
        if self.instructions:
            messages.append({"role": "system", "content": self.instructions})
            
        messages.append({"role": "user", "content": query})
        
        response = self.model.chat(messages)
        
        return {"content": response}

class ExaSearchTool:
    def __init__(self, api_key: str, include_domains: List[str] = None, num_results: int = 5):
        self.api_key = api_key
        self.include_domains = include_domains or []
        self.num_results = num_results
        
    def search(self, query: str) -> str:
        """
        Implementação para pesquisa Exa.
        """
        import requests
        
        try:
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            
            payload = {
                "query": query,
                "numResults": self.num_results
            }
            
            if self.include_domains:
                payload["includeDomains"] = self.include_domains
                
            response = requests.post(
                "https://api.exa.ai/search",
                headers=headers,
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                results = []
                
                for result in data.get("results", []):
                    title = result.get("title", "Sem título")
                    url = result.get("url", "Sem URL")
                    text = result.get("text", "Nenhum texto disponível")
                    results.append(f"### {title}\n{url}\n\n{text[:300]}...\n\n")
                
                return "\n".join(results) if results else "No results found."
            else:
                return f"Erro de pesquisa: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Erro durante pesquisa na web: {str(e)}"

# Streamlit app.
st.title("AgentsRAG e Web de pesquisa de informações médicas")

# Estado da inicialização.
if 'google_api_key' not in st.session_state:
    st.session_state.google_api_key = ""
if 'pinecone_api_key' not in st.session_state:
    st.session_state.pinecone_api_key = os.getenv("PINECONE_API_KEY")
if 'pinecone_environment' not in st.session_state:
    st.session_state.pinecone_environment = os.getenv("PINECONE_ENVIRONMENT")
if 'model_version' not in st.session_state:
    st.session_state.model_version = "llama-3.1-8b-instant"  # Default to Groq model
if 'vector_store' not in st.session_state:
    st.session_state.vector_store = None
if 'history' not in st.session_state:
    st.session_state.history = []
if 'exa_api_key' not in st.session_state:
    st.session_state.exa_api_key = os.getenv("EXA_API_KEY")
if 'use_web_search' not in st.session_state:
    st.session_state.use_web_search = False
if 'force_web_search' not in st.session_state:
    st.session_state.force_web_search = False
if 'similarity_threshold' not in st.session_state:
    st.session_state.similarity_threshold = 0.7
if 'rag_enabled' not in st.session_state:
    st.session_state.rag_enabled = True

# Sidebar Configuration
st.sidebar.header("🤖 Configurações do Agent")

st.sidebar.header("📦 Seleção de Modelo")
model_help = """
- llama-3.1-8b-instant: modelo Llama da Groq para inferência rápida.
- llama3-8b-8192: modelo Llama da Groq com 8B parâmetros e 8192 comprimentos de contexto.
- llama-3.3-70b-versatile: modelo Llama da Groq com 70B parâmetros e capacidades versáteis.
- llama-3.2-3b-preview: modelo Llama da Groq com 3B parâmetros (prévia).
- llama-3.2-1b-preview: modelo Llama da Groq com 1B parâmetros (prévia).
- mistral-saba-24b: modelo Mistral com 24B parâmetros e 32K de comprimento de contexto.
- qwen-2.5-coder-32b: modelo Alibaba Cloud com 32B parâmetros e 128K de comprimento de contexto.
- qwen-2.5-32b: modelo Alibaba Cloud com 32B parâmetros e 128K de comprimento de contexto.
- deepseek-r1-distill-qwen-32b: modelo DeepSeek com 32B parâmetros, 128K de comprimento de contexto e 16,384 tokens de entrada.
- deepseek-r1-distill-llama-70b-specdec: modelo DeepSeek com 70B parâmetros, 128K de comprimento de contexto e 16,384 tokens de entrada.
"""
model_selection = st.sidebar.selectbox(
    "Escolha o modelo LLM", 
    options=[
        "llama-3.1-8b-instant", 
        "llama3-8b-8192", 
        "llama-3.3-70b-versatile", 
        "llama-3.2-3b-preview",
        "llama-3.2-1b-preview",
        "mistral-saba-24b",
        "qwen-2.5-coder-32b",
        "qwen-2.5-32b",
        "deepseek-r1-distill-qwen-32b",
        "deepseek-r1-distill-llama-70b"
    ],
    help=model_help
)

st.sidebar.header("🔍 Configuração RAG")
st.session_state.rag_enabled = st.sidebar.toggle("Habilitar modo RAG", value=st.session_state.rag_enabled)

if st.sidebar.button("🗑️ Limpar Chat"):
    st.session_state.history = []
    st.rerun()

if st.session_state.rag_enabled:
    st.sidebar.header("🔑 Configuração da API")
    pinecone_api_key = st.sidebar.text_input("Pinecone API Key", type="password", value=st.session_state.pinecone_api_key)
    pinecone_environment = st.sidebar.text_input("Pinecone Environment", 
                                     placeholder="us-east-1",
                                     value=st.session_state.pinecone_environment)

    st.session_state.pinecone_api_key = pinecone_api_key
    st.session_state.pinecone_environment = pinecone_environment
    
    st.sidebar.header("🎯 Configuração de Pesquisa")
    st.session_state.similarity_threshold = st.sidebar.slider(
        "Limite de similaridade de documentos",
        min_value=0.0,
        max_value=1.0,
        value=0.70,
        help="Valores mais baixos retornarão mais documentos, mas podem ser menos relevantes. Valores mais altos são mais rigorosos."
    )

st.sidebar.header("🌐 Config de pesquisa na Web")
st.session_state.use_web_search = st.sidebar.checkbox("Habilitar fallback de pesquisa na Web", value=st.session_state.use_web_search)

if st.session_state.use_web_search:
    exa_api_key = st.sidebar.text_input(
        "Exa AI API Key", 
        type="password",
        value=st.session_state.exa_api_key,
        help="Necessário para fallback de pesquisa na web quando nenhum documento relevante for encontrado"
    )
    st.session_state.exa_api_key = exa_api_key
    
    # Filtragem de domínio (opcional).
    default_domains = ["wikipedia.org"]
    custom_domains = st.sidebar.text_input(
        "Domínios personalizados (separados por vírgula)", 
        value=",".join(default_domains),
        help="Insira os domínios para pesquisar, por exemplo: wikipedia.org"
    )
    search_domains = [d.strip() for d in custom_domains.split(",") if d.strip()]

def init_pinecone() -> Optional[bool]:
    """Inicializar o cliente Pinecone com as configurações definidas."""
    if not all([st.session_state.pinecone_api_key, st.session_state.pinecone_environment]):
        return None
    try:
        # Criar uma instância da classe Pinecone em vez de usar init()
        pc = Pinecone(api_key=st.session_state.pinecone_api_key)
        # Armazenar a instância para uso posterior
        st.session_state.pinecone_client = pc
        return True
    except Exception as e:
        st.error(f"🔴 Pinecone conexão falhou: {str(e)}")
        return None

def get_web_search_agent() -> CustomAgent:
    """Inicializar um agente de pesquisa na web."""
    exa_tool = ExaSearchTool(
        api_key=st.session_state.exa_api_key,
        include_domains=search_domains,
        num_results=5
    )
    
    groq_client = GroqClient(api_key=os.getenv("GROQ_API_KEY"), model=model_selection)
    
    return CustomAgent(
        name="Web Search Agent",
        model=groq_client,
        instructions="""You are a web medical research expert. Your job is to:
        1. Search the web for relevant information about the query
        2. Compile and summarize the most relevant information
        3. Include sources in your answer
        4. Always answer in Portuguese.
        5. You are a helpful, respectful, and honest medical assistant. Always answer in the most helpful way possible while remaining confident. Your answers should not include harmful, unethical, racist, sexist, toxic, dangerous, or illegal content. Make sure your answers are socially impartial and positive in nature. If a question does not make sense or is not factually coherent, explain why, rather than answering something incorrect. If you do not know the answer to a question, do not share false information. 
        """,
        show_tool_calls=True,
        markdown=True,
    )

def get_rag_agent() -> CustomAgent:
    """Inicializa o agente RAG principal."""
    groq_client = GroqClient(api_key=os.getenv("GROQ_API_KEY"), model=model_selection)
    
    return CustomAgent(
        name="RAG Agent",
        model=groq_client,
        instructions="""You are an Intelligent Agent specialized in providing accurate medical answers.

        When asked a question:
        - Analyze the question and answer the question with what you know.
        
        When given context from documents:
        - Focus on information from the provided documents
        - Be precise and cite specific details
        
        When given web search results:
        - Clearly indicate that the information comes from web search
        - Synthesize the information clearly
        
        Always maintain high accuracy and clarity in your responses.
        
        Always answer in Portuguese.
        
        You are a helpful, respectful, and honest medical assistant. Always answer in the most helpful way possible while remaining confident. Your answers should not include harmful, unethical, racist, sexist, toxic, dangerous, or illegal content. Make sure your answers are socially impartial and positive in nature. If a question does not make sense or is not factually coherent, explain why, rather than answering something incorrect. If you do not know the answer to a question, do not share false information
                """,
        show_tool_calls=True,
        markdown=True,
    )

def check_document_relevance(query: str, vector_store, threshold: float = 0.7) -> tuple[bool, List]:
    if not vector_store:
        return False, []
        
    # Primeiro, obtemos os documentos com suas pontuações
    docs_and_scores = vector_store.similarity_search_with_score(query, k=5)
    
    # Filtramos os documentos com base no limiar de similaridade
    filtered_docs = []
    for doc, score in docs_and_scores:
        if score >= threshold:
            # Verifica se o documento tem o conteúdo esperado
            if hasattr(doc, 'page_content') or hasattr(doc, 'text'):
                filtered_docs.append(doc)
            else:
                logger.warning(f"Documento sem chave 'text' ou 'page_content' encontrado. Score: {score}")
                logger.warning(f"Document structure: {dir(doc)}")
    
    return bool(filtered_docs), filtered_docs

def init_vector_store():
    """Inicializa o armazenamento de vetores, caso ainda não tenha feito isso."""
    if st.session_state.vector_store is None and st.session_state.rag_enabled:
        pinecone_initialized = init_pinecone()
        if pinecone_initialized:
            try:
                pc = st.session_state.pinecone_client
                
                # Obter uma lista de índices existentes
                existing_indexes = [idx.name for idx in pc.list_indexes()]
                
                # Calcular a dimensão dos embeddings
                import numpy as np
                test_embedding = embedder.embed_query("test")
                
                if hasattr(test_embedding, 'shape'): 
                    embedding_size = test_embedding.shape[0]
                else:
                    embedding_size = np.array(test_embedding).shape[0]
                
                # Criar o índice se ele não existir
                if INDEX_NAME not in existing_indexes:
                    pc.create_index(
                        name=INDEX_NAME,
                        dimension=embedding_size,
                        metric="cosine",
                        spec=ServerlessSpec(
                            cloud=st.session_state.pinecone_environment.split('-')[0],  # Usando o ambiente para identificar o cloud provider
                            region=st.session_state.pinecone_environment.split('-')[1] if '-' in st.session_state.pinecone_environment else "us-east-1"
                        )
                    )
                    st.info(f"Created new index: {INDEX_NAME}")
                
                # Inicializa o PineconeVectorStore
                st.session_state.vector_store = PineconeVectorStore(
                    index_name=INDEX_NAME,
                    embedding=embedder
                )
                st.success("✅ Conectado ao banco de dados de vetores Pinecone")
            except Exception as e:
                st.error(f"❌ Falha de conexão com o banco de dados Pinecone: {str(e)}")

# Inicializa armazenamento de vetores se o RAG estiver habilitado.
if st.session_state.rag_enabled:
    init_vector_store()

# UI/UX.
chat_col, toggle_col = st.columns([0.9, 0.1])

with chat_col:
    prompt = st.chat_input("Pesquise informações médicas" if st.session_state.rag_enabled else "Pergunte-me qualquer coisa sobre informações médicas...")

with toggle_col:
    st.session_state.force_web_search = st.toggle('🌐', help="Force web search")

# Exibe histórico de bate-papo.
for message in st.session_state.history:
    with st.chat_message(message["role"]):
        st.write(message["content"])

if prompt:
    st.session_state.history.append({"role": "user", "content": prompt})
    with st.chat_message("user"):
        st.write(prompt)

    if st.session_state.rag_enabled:
        pinecone_initialized = init_pinecone()
        
        with st.spinner("🤔 Avaliando a consulta..."):
            try:
                rewritten_query = prompt
                
                with st.expander("Avaliando a consulta"):
                    st.write(f"User's Prompt: {prompt}")
            except Exception as e:
                st.error(f"❌ Erro ao reescrever consulta: {str(e)}")
                rewritten_query = prompt

        context = ""
        docs = []
        if not st.session_state.force_web_search and st.session_state.vector_store:
            try:
                # Obter documentos com pontuações
                docs_and_scores = st.session_state.vector_store.similarity_search_with_score(
                    rewritten_query, k=5
                )
                
                # Filtrar documentos que atendem ao limiar de similaridade
                docs = [doc for doc, score in docs_and_scores if score >= st.session_state.similarity_threshold]
                
                if docs:
                    # Modificação aqui: verificação de segurança para acessar o conteúdo do documento
                    context = "\n\n".join([
                        getattr(d, 'page_content', getattr(d, 'text', 'Nenhum conteúdo disponível'))
                        for d in docs
                    ])
                    
                    # Adicionar mais informações de depuração se necessário
                    with st.expander("Debug - Estrutura de Documentos", expanded=False):
                        for i, doc in enumerate(docs):
                            st.write(f"Documento {i} estrutura: {dir(doc)}")
                            st.write(f"Documento {i} metadata: {doc.metadata if hasattr(doc, 'metadata') else 'Sem metadata'}")
                    
                    st.info(f"📊 Found {len(docs)} relevant documents (similarity > {st.session_state.similarity_threshold})")
                elif st.session_state.use_web_search:
                    st.info("🔄 Nenhum documento relevante encontrado no banco de dados, recorrendo à pesquisa na web...")
            except Exception as e:
                st.error(f"❌ Erro ao recuperar documentos: {str(e)}")
                if st.session_state.use_web_search:
                    st.info("🔄 Ocorreu um erro na recuperação de documentos, recorrendo à pesquisa na web...")

        if (st.session_state.force_web_search or not context) and st.session_state.use_web_search and st.session_state.exa_api_key:
            with st.spinner("🔍 Searching the web..."):
                try:
                    web_search_agent = get_web_search_agent()
                    # Usa a ferramenta Exa diretamente para pesquisa.
                    exa_tool = ExaSearchTool(
                        api_key=st.session_state.exa_api_key,
                        include_domains=search_domains,
                        num_results=5
                    )
                    web_results = exa_tool.search(rewritten_query)
                    if web_results:
                        context = f"Web Search Results:\n{web_results}"
                        if st.session_state.force_web_search:
                            st.info("ℹ️ Usando a pesquisa na web conforme solicitado via alternância.")
                        else:
                            st.info("ℹ️ Usando a pesquisa na web como alternativa, pois nenhum documento relevante foi encontrado.")
                except Exception as e:
                    st.error(f"❌ Erro de pesquisa na web: {str(e)}")

        with st.spinner("🤖 Thinking..."):
            try:
                rag_agent = get_rag_agent()
                
                if context:
                    full_prompt = f"""Context: {context}

Original Question: {prompt}
Please provide a comprehensive answer based on the available information."""
                else:
                    full_prompt = f"Original question: {prompt}\n"
                    st.info("ℹ️ Nenhuma informação relevante encontrada em documentos ou pesquisa na web.")

                response = rag_agent.run(full_prompt)
                
                st.session_state.history.append({
                    "role": "assistant",
                    "content": response["content"]
                })
                
                with st.chat_message("assistant"):
                    st.write(response["content"])
                        
                    if not st.session_state.force_web_search and 'docs' in locals() and docs:
                        with st.expander("🔍 Ver fontes de documentos"):
                            for i, doc in enumerate(docs, 1):
                                # Verificar se metadata existe
                                if hasattr(doc, 'metadata'):
                                    source_type = doc.metadata.get("source_type", "unknown")
                                    source_icon = "📄" if source_type == "pdf" else "🌐"
                                    source_name = doc.metadata.get("file_name" if source_type == "pdf" else "url", "unknown")
                                    st.write(f"{source_icon} Source {i} from {source_name}:")
                                    # Usar getattr para acessar o conteúdo do documento com segurança
                                    content = getattr(doc, 'page_content', getattr(doc, 'text', 'Nenhum conteúdo disponível'))
                                    st.write(f"{content[:200]}...")
                                else:
                                    st.write(f"Source {i}: Metadata não disponível")
                                    content = getattr(doc, 'page_content', getattr(doc, 'text', 'Nenhum conteúdo disponível'))
                                    st.write(f"{content[:200]}...")

            except Exception as e:
                st.error(f"❌ Erro ao gerar resposta: {str(e)}")

    else:
        with st.spinner("🤖 Thinking..."):
            try:
                rag_agent = get_rag_agent()
                
                context = ""
                if st.session_state.force_web_search and st.session_state.use_web_search and st.session_state.exa_api_key:
                    with st.spinner("🔍 Pesquisando na web..."):
                        try:
                            exa_tool = ExaSearchTool(
                                api_key=st.session_state.exa_api_key,
                                include_domains=search_domains,
                                num_results=5
                            )
                            web_results = exa_tool.search(prompt)
                            if web_results:
                                context = f"Resultados da pesquisa na Web:\n{web_results}"
                                st.info("ℹ️ Usando a pesquisa na web conforme solicitado.")
                        except Exception as e:
                            st.error(f"❌ Erro de pesquisa na web: {str(e)}")
                
                if context:
                    full_prompt = f"""Context: {context}

Question: {prompt}

Please provide a comprehensive answer based on the available information."""
                else:
                    full_prompt = prompt

                response = rag_agent.run(full_prompt)
                response_content = response["content"]
                
                import re
                think_pattern = r'<think>(.*?)</think>'
                think_match = re.search(think_pattern, response_content, re.DOTALL)
                
                if think_match:
                    thinking_process = think_match.group(1).strip()
                    final_response = re.sub(think_pattern, '', response_content, flags=re.DOTALL).strip()
                else:
                    thinking_process = None
                    final_response = response_content
                
                st.session_state.history.append({
                    "role": "assistant",
                    "content": final_response
                })
                
                with st.chat_message("assistant"):
                    if thinking_process:
                        with st.expander("🤔 Veja o processo de pensamento"):
                            st.markdown(thinking_process)
                    st.markdown(final_response)

            except Exception as e:
                st.error(f"❌ Erro ao gerar resposta: {str(e)}")

else:
    st.warning("Você pode testar outros modelos para melhores resultados! Ou acionar os agentes de buscas Web!")