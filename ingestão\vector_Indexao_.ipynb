{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "aYpmmfQ-ZbIh"}, "outputs": [], "source": ["!pip install -q -U langchain-community langchain-pinecone pinecone-client sentence_transformers python-dotenv"]}, {"cell_type": "code", "source": ["!pip install --upgrade nltk -q"], "metadata": {"id": "JaILGk6GDrX6"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["!pip install unstructured -q\n", "!pip install unstructured[local-inference] -q\n", "!pip install detectron2@git+https://github.com/facebookresearch/detectron2.git@v0.6#egg=detectron2 -q\n", "!apt-get install -q poppler-utils"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XbL5zdf9aG8a", "outputId": "34933415-c536-425b-9b51-a576f6e6c396"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "Reading package lists...\n", "Building dependency tree...\n", "Reading state information...\n", "poppler-utils is already the newest version (22.02.0-2ubuntu0.6).\n", "0 upgraded, 0 newly installed, 0 to remove and 29 not upgraded.\n"]}]}, {"cell_type": "markdown", "source": ["https://python.langchain.com/en/latest/modules/indexes/document_loaders/examples/directory_loader.html"], "metadata": {"id": "2AssnWUVlxtH"}}, {"cell_type": "code", "source": ["import nltk\n", "nltk.download('punkt')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xQiYU5akxiUZ", "outputId": "a8ea407e-612d-4b89-c377-e1f018a24424"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["[nltk_data] Downloading package punkt to /root/nltk_data...\n", "[nltk_data]   Package punkt is already up-to-date!\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["from langchain.document_loaders import DirectoryLoader\n", "\n", "directory = '/content/data/'\n", "\n", "def load_docs(directory):\n", "  loader = DirectoryLoader(directory)\n", "  documents = loader.load()\n", "  return documents\n", "\n", "documents = load_docs(directory)\n", "len(documents)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fulYnj9nZr3n", "outputId": "fff2f81f-b393-4e4f-b327-e01e3aa3e17c"}, "execution_count": 6, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["1"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "markdown", "source": ["https://python.langchain.com/en/latest/modules/indexes/text_splitters/getting_started.html"], "metadata": {"id": "VwaCosqsogzw"}}, {"cell_type": "code", "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "\n", "def split_docs(documents,chunk_size=1000,chunk_overlap=200):\n", "  text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)\n", "  docs = text_splitter.split_documents(documents)\n", "  return docs\n", "\n", "docs = split_docs(documents)\n", "print(len(docs))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5lF8jA6xZ0Hm", "outputId": "c5da5141-b47f-45f2-a130-6cd393201873"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["7228\n"]}]}, {"cell_type": "code", "source": ["print(docs[3].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tVcFjgHJZ8S9", "outputId": "22f2643e-b6d8-4391-afe7-295aad8bb69a"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["overdose. Não é recomendado tentar forçar a pessoa a vomitar. Se houver potencial de toxicidade, recomenda-se o antídoto acetilcisteína. A medicação geralmente é administrada por pelo menos 24 horas. Cuidados psiquiátricos podem ser necessários após a recuperação. Um transplante de fígado pode ser necessário se o dano ao fígado se tornar grave. A necessidade de transplante geralmente se baseia em pH sanguíneo baixo, lactato sanguíneo elevado, coagulação sanguínea deficiente ou encefalopatia hepática significativa. Com tratamento precoce, a insuficiência hepática é rara. A morte ocorre em cerca de 0,1% dos casos. O envenenamento por paracetamol foi descrito pela primeira vez na década de 1960. As taxas de envenenamento variam significativamente entre as regiões do mundo. Nos Estados Unidos ocorrem mais de 100.000 casos por ano. No Reino Unido é o medicamento responsável pelo maior número de overdoses. As crianças pequenas são mais comumente afetadas. Nos Estados Unidos e no Reino\n"]}]}, {"cell_type": "code", "source": ["from langchain.embeddings import SentenceTransformerEmbeddings\n", "embeddings = SentenceTransformerEmbeddings(model_name=\"all-MiniLM-L6-v2\")"], "metadata": {"id": "F5GY9voPa0av", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "4d0eeb42-ff6a-447f-8059-7311e6e9daa8"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-9-4ebf07722f11>:2: LangChainDeprecationWarning: The class `HuggingFaceEmbeddings` was deprecated in LangChain 0.2.2 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-huggingface package and should be used instead. To use it run `pip install -U :class:`~langchain-huggingface` and import as `from :class:`~langchain_huggingface import HuggingFaceEmbeddings``.\n", "  embeddings = SentenceTransformerEmbeddings(model_name=\"all-MiniLM-L6-v2\")\n", "/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_auth.py:94: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}]}, {"cell_type": "markdown", "source": ["https://python.langchain.com/en/latest/modules/indexes/vectorstores/examples/pinecone.html"], "metadata": {"id": "vySq5oI5sU5V"}}, {"cell_type": "code", "source": ["import os\n", "from dotenv import load_dotenv\n", "\n", "pinecone_api_key = os.getenv(\"PINECONE_API_KEY\")\n", "load_dotenv()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9sL0R8P25QyS", "outputId": "909a8df6-bd0b-41d6-d36a-e7b7f84c0f4f"}, "execution_count": 10, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["True"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["import os\n", "import pinecone\n", "from langchain_pinecone import PineconeVectorStore\n", "import time\n", "\n", "environment = \"aws\"\n", "\n", "pc = pinecone.Pinecone(api_key=pinecone_api_key, environment=environment)\n", "\n", "index_name = \"medinfo\"\n", "existing_indexes = [index_info[\"name\"] for index_info in pc.list_indexes()]\n", "if index_name not in existing_indexes:\n", "    pc.create_index(\n", "        name=index_name,\n", "        dimension=384,\n", "        metric=\"cosine\",\n", "        spec=pinecone.ServerlessSpec(cloud=\"aws\", region=\"us-east-1\"),\n", "    )\n", "    while not pc.describe_index(index_name).status[\"ready\"]:\n", "        time.sleep(1)\n", "\n", "vector_store = PineconeVectorStore.from_documents(docs, embeddings, index_name=index_name)"], "metadata": {"id": "qmDzAOHztCnv"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["query = \"O que é envenenamento por paracetamol e explica em detalhes?\"\n", "docs = vector_store.similarity_search(query)\n", "print(docs[0].page_content)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pFQpwL4yyqu-", "outputId": "ee6e743a-a73e-4f0d-a746-b888f06c5c93"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\"O que é Tramadol/paracetamol e explique em detalhes?\"; </s>\"\n"]}]}]}