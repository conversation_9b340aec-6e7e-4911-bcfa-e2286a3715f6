# -*- coding: utf-8 -*-
"""vector_Indexação.ipynb"""

!pip install -q -U langchain-community langchain-pinecone pinecone-client sentence_transformers python-dotenv

!pip install --upgrade nltk -q

!pip install unstructured -q
!pip install unstructured[local-inference] -q
!pip install detectron2@git+https://github.com/facebookresearch/detectron2.git@v0.6#egg=detectron2 -q
!apt-get install -q poppler-utils

"""https://python.langchain.com/en/latest/modules/indexes/document_loaders/examples/directory_loader.html"""

import nltk
nltk.download('punkt')

from langchain.document_loaders import DirectoryLoader

directory = '/content/data/'

def load_docs(directory):
  loader = DirectoryLoader(directory)
  documents = loader.load()
  return documents

documents = load_docs(directory)
len(documents)

"""https://python.langchain.com/en/latest/modules/indexes/text_splitters/getting_started.html"""

from langchain.text_splitter import RecursiveCharacterTextSplitter

def split_docs(documents,chunk_size=1000,chunk_overlap=200):
  text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
  docs = text_splitter.split_documents(documents)
  return docs

docs = split_docs(documents)
print(len(docs))

print(docs[3].page_content)

from langchain.embeddings import SentenceTransformerEmbeddings
embeddings = SentenceTransformerEmbeddings(model_name="all-MiniLM-L6-v2")

"""https://python.langchain.com/en/latest/modules/indexes/vectorstores/examples/pinecone.html"""

import os
from dotenv import load_dotenv

pinecone_api_key = os.getenv("PINECONE_API_KEY")
load_dotenv()

import os
import pinecone
from langchain_pinecone import PineconeVectorStore
import time

environment = "aws"

pc = pinecone.Pinecone(api_key=pinecone_api_key, environment=environment)

index_name = "medinfo"
existing_indexes = [index_info["name"] for index_info in pc.list_indexes()]
if index_name not in existing_indexes:
    pc.create_index(
        name=index_name,
        dimension=384,
        metric="cosine",
        spec=pinecone.ServerlessSpec(cloud="aws", region="us-east-1"),
    )
    while not pc.describe_index(index_name).status["ready"]:
        time.sleep(1)

vector_store = PineconeVectorStore.from_documents(docs, embeddings, index_name=index_name)

query = "O que é envenenamento por paracetamol e explica em detalhes?"
docs = vector_store.similarity_search(query)
print(docs[0].page_content)