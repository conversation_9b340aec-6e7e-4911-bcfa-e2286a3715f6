# 🏥 MedInfo RAG Agent

Um assistente médico inteligente com capacidades de pesquisa avançada, utilizando tecnologias de IA de ponta.

## ✨ Funcionalidades

### 🧠 RAG (Retrieval-Augmented Generation)
- Busca em base de conhecimento médico usando Pinecone
- Embeddings com modelo `sentence-transformers/all-MiniLM-L6-v2`
- Filtragem por similaridade configurável

### 🌐 Pesquisa Web Inteligente
- Integração com Exa AI para pesquisa web
- Domínios médicos confiáveis pré-configurados
- Cache de resultados para melhor performance

### 🤖 Modelos de IA Avançados
- Múltiplos modelos Groq disponíveis:
  - llama-3.1-8b-instant (padrão)
  - llama3-8b-8192
  - llama-3.3-70b-versatile
  - mistral-saba-24b
  - qwen-2.5-coder-32b
  - E outros...

### 📊 Interface Aprimorada
- Validação em tempo real das chaves API
- Teste de conectividade das APIs
- Estatísticas da conversa
- Exportação de conversas em Markdown
- Status do sistema em tempo real

## 🚀 Instalação

1. Clone o repositório:
```bash
git clone <repository-url>
cd victor-tcc
```

2. Instale as dependências:
```bash
pip install -r medinfo_rag_agent/requirements.txt
```

3. Execute a aplicação:
```bash
streamlit run medinfo_rag_agent/medinfo_rag_agent.py
```

## 🔑 Configuração de API Keys

### 🔒 Segurança das Chaves
- **Groq e Exa**: Inseridas pelo usuário na interface (não armazenadas)
- **Pinecone**: Armazenada no arquivo .env (não visível na interface)
- **HuggingFace**: Armazenada no arquivo .env (opcional)

### Groq API Key
1. Acesse [Groq Console](https://console.groq.com/)
2. Crie uma conta e gere uma API key
3. Insira a chave na interface (deve começar com `gsk_`)

### Exa AI API Key
1. Acesse [Exa AI](https://exa.ai/)
2. Crie uma conta e gere uma API key
3. Insira a chave na interface (formato UUID)

### Pinecone API Key (Opcional - para RAG)
1. Acesse [Pinecone](https://www.pinecone.io/)
2. Crie uma conta e gere uma API key
3. **Configure no arquivo .env**: `PINECONE_API_KEY=sua_chave_aqui`
4. Configure o ambiente na interface (ex: `us-east-1`)

## 🛠️ Funcionalidades Avançadas

### Validação de API Keys
- Verificação de formato em tempo real
- Teste de conectividade opcional
- Feedback visual do status

### Cache e Performance
- Cache de embeddings para melhor performance
- Cache de resultados de pesquisa web (1 hora)
- Otimizações de carregamento

### Exportação e Histórico
- Exportação de conversas em Markdown
- Estatísticas detalhadas da conversa
- Histórico persistente durante a sessão

### Tratamento de Erros
- Validação robusta de entrada
- Mensagens de erro informativas
- Fallbacks para diferentes cenários

## 📋 Uso

1. **Configure as API Keys**: Insira suas chaves API na barra lateral
2. **Escolha o Modo**: Habilite RAG e/ou pesquisa web conforme necessário
3. **Selecione o Modelo**: Escolha o modelo de IA mais adequado
4. **Faça Perguntas**: Digite suas perguntas médicas no chat
5. **Exporte Conversas**: Use o botão de exportação para salvar conversas

## 🔧 Configurações Avançadas

### Similaridade de Documentos
- Ajuste o limiar de similaridade (0.0 - 1.0)
- Valores mais baixos = mais documentos, menos precisão
- Valores mais altos = menos documentos, mais precisão

### Domínios de Pesquisa Web
- Configure domínios personalizados para pesquisa
- Padrão: Wikipedia, PubMed, Mayo Clinic
- Separar múltiplos domínios por vírgula

## 📊 Monitoramento

A aplicação fornece informações em tempo real sobre:
- Status das conexões API
- Número de mensagens na conversa
- Estado do sistema RAG
- Performance das consultas

## 🚨 Importante

⚠️ **Aviso Médico**: Este assistente é apenas para fins informativos e educacionais. Sempre consulte profissionais de saúde qualificados para diagnósticos e tratamentos médicos.

## 🤝 Contribuição

Contribuições são bem-vindas! Por favor, abra issues ou pull requests para melhorias.

## 📄 Licença

Este projeto está licenciado sob a licença MIT.
