# Arquivo de exemplo para configuração das variáveis de ambiente
# Copie este arquivo para .env e configure suas chaves API

# =============================================================================
# PINECONE CONFIGURATION (Obrigatório para RAG)
# =============================================================================
# Acesse: https://www.pinecone.io/
# Crie uma conta gratuita e gere uma API key
PINECONE_API_KEY=your_pinecone_api_key_here

# =============================================================================
# HUGGINGFACE CONFIGURATION (Opcional)
# =============================================================================
# Acesse: https://huggingface.co/settings/tokens
# Gere um token de acesso (opcional, melhora performance)
HUGGINGFACE_API_TOKEN=your_huggingface_token_here

# =============================================================================
# NOTAS IMPORTANTES
# =============================================================================
# 
# 1. GROQ API KEY: Será inserida pelo usuário na interface
#    - Não configure aqui por segurança
#    - Formato: gsk_...
#
# 2. EXA API KEY: Será inserida pelo usuário na interface  
#    - Não configure aqui por segurança
#    - Formato: UUID
#
# 3. PINECONE API KEY: Deve ser configurada aqui
#    - Não será visível na interface por segurança
#    - Necessária para funcionalidade RAG
#
# 4. HUGGINGFACE TOKEN: Opcional, melhora performance
#    - Evita rate limits nos modelos de embedding
#    - Não obrigatório para funcionamento básico
#
# =============================================================================
# EXEMPLO DE CONFIGURAÇÃO
# =============================================================================
#
# PINECONE_API_KEY=12345678-1234-1234-1234-123456789abc
# HUGGINGFACE_API_TOKEN=hf_abcdefghijklmnopqrstuvwxyz123456789
#
# =============================================================================
