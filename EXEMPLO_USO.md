# 🚀 Exemplo de Uso - MedInfo RAG Agent

## Como Executar o Aplicativo

### 1. Instalação das Dependências
```bash
cd medinfo_rag_agent
pip install -r requirements.txt
```

### 2. Executar o Aplicativo
```bash
streamlit run medinfo_rag_agent.py
```

O aplicativo será aberto no navegador em `http://localhost:8501`

## 🔑 Configuração Inicial

### Passo 1: Inserir API Keys
1. **Groq API Key** (Obrigatória):
   - Acesse: https://console.groq.com/
   - Crie uma conta gratuita
   - Gere uma API key (formato: `gsk_...`)
   - Insira na barra lateral

2. **Exa AI API Key** (Para pesquisa web):
   - Acesse: https://exa.ai/
   - Crie uma conta
   - Gere uma API key (formato UUID)
   - Insira na barra lateral quando habilitar pesquisa web

3. **Pinecone API Key** (Para RAG):
   - Acesse: https://www.pinecone.io/
   - Crie uma conta gratuita
   - Gere uma API key
   - Configure o ambiente (ex: `us-east-1`)

### Passo 2: Configurar Funcionalidades
- ✅ **Habilitar RAG**: Para usar base de conhecimento médico
- ✅ **Habilitar pesquisa web**: Para buscar informações online

## 💬 Exemplos de Perguntas

### Perguntas Básicas
```
"O que é diabetes tipo 2?"
"Quais são os sintomas de hipertensão?"
"Como prevenir doenças cardiovasculares?"
```

### Perguntas Específicas
```
"Qual a diferença entre diabetes tipo 1 e tipo 2?"
"Quais medicamentos são usados para tratar depressão?"
"Como funciona a insulina no organismo?"
```

### Perguntas com Pesquisa Web
```
"Últimas pesquisas sobre Alzheimer"
"Novos tratamentos para câncer de mama"
"Estudos recentes sobre COVID-19"
```

## 🎯 Funcionalidades Avançadas

### Validação de API Keys
- O sistema valida automaticamente o formato das chaves
- Botões de teste permitem verificar conectividade
- Status em tempo real na barra lateral

### Configurações de Pesquisa
- **Similaridade de documentos**: Ajuste a precisão (0.0-1.0)
- **Domínios personalizados**: Configure sites específicos
- **Modelos de IA**: Escolha entre vários modelos Groq

### Exportação e Histórico
- **Exportar conversa**: Salve em formato Markdown
- **Estatísticas**: Veja métricas da conversa
- **Limpar chat**: Reset da conversa

## 🔧 Solução de Problemas

### Erro: "Groq API Key necessária"
- Verifique se inseriu a chave corretamente
- Confirme que a chave começa com `gsk_`
- Teste a conectividade com o botão de teste

### Erro: "Exa API Key necessária"
- Verifique se a chave está no formato UUID
- Confirme que habilitou a pesquisa web
- Teste a conectividade

### Erro: "Pinecone conexão falhou"
- Verifique a API key do Pinecone
- Confirme o ambiente (ex: `us-east-1`)
- Verifique sua conexão com internet

### Performance Lenta
- Use modelos menores (ex: `llama-3.1-8b-instant`)
- Reduza o número de resultados de pesquisa
- Ajuste o limiar de similaridade

## 📊 Interpretando Resultados

### Status do Sistema
- ✅ **Verde**: Funcionando corretamente
- ⚠️ **Amarelo**: Configuração necessária
- ❌ **Vermelho**: Erro ou não configurado

### Fontes de Informação
- **RAG**: Informações da base de conhecimento
- **Web Search**: Resultados de pesquisa online
- **Modelo**: Conhecimento do modelo de IA

### Qualidade das Respostas
- Respostas com fontes são mais confiáveis
- Múltiplas fontes indicam maior precisão
- Sempre verifique informações médicas importantes

## ⚠️ Importante

**Aviso Médico**: Este assistente é apenas para fins informativos e educacionais. Sempre consulte profissionais de saúde qualificados para diagnósticos e tratamentos médicos.

## 🆘 Suporte

Se encontrar problemas:
1. Verifique as configurações de API
2. Consulte os logs de erro
3. Reinicie o aplicativo
4. Verifique a conexão com internet

Para mais ajuda, consulte o README.md ou abra uma issue no repositório.
